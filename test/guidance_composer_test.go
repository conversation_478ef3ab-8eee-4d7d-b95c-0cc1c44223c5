package test

import (
	"context"
	"testing"

	"github.com/RemedyMate/remedymate-backend/domain/entities"
	"github.com/RemedyMate/remedymate-backend/infrastructure/guidance"
)

// MockLLMClient for testing
type MockLLMClient struct{}

func (m *MockLLMClient) ClassifyTriage(ctx context.Context, prompt string) (string, error) {
	return "GREEN", nil
}

func (m *MockLLMClient) ComposeGuidance(ctx context.Context, blocks entities.ContentTranslation, topicKey, language string) (*entities.GuidanceCard, error) {
	// Simulate LLM intelligently structuring the content
	return &entities.GuidanceCard{
		TopicKey: topicKey,
		Language: language,
		SelfCare: []string{
			"Enhanced self-care recommendation 1 (structured by LLM)",
			"Enhanced self-care recommendation 2 (structured by LLM)",
		},
		OTCCategories: blocks.OTCCategories,
		SeekCareIf: []string{
			"Enhanced seek care guidance (structured by LLM)",
		},
		Disclaimer: "Enhanced disclaimer structured by LLM for better readability",
		IsOffline:  false,
	}, nil
}

// MockContentService for testing
type MockContentService struct{}

func (m *MockContentService) GetApprovedBlocks() ([]entities.ApprovedBlock, error) {
	return []entities.ApprovedBlock{}, nil
}

func (m *MockContentService) GetContentByTopic(topicKey, language string) (*entities.ContentTranslation, error) {
	return &entities.ContentTranslation{
		SelfCare: []string{
			"Basic self-care recommendation 1",
			"Basic self-care recommendation 2",
		},
		OTCCategories: []entities.OTCCategory{
			{CategoryName: "Pain relievers", SafetyNote: "Follow package instructions"},
		},
		SeekCareIf: []string{
			"Basic seek care guidance",
		},
		Disclaimer: "Basic disclaimer",
	}, nil
}

func TestGuidanceComposerWithLLM(t *testing.T) {
	// Setup
	mockContentService := &MockContentService{}
	mockLLMClient := &MockLLMClient{}
	
	composer := guidance.NewGuidanceComposerService(mockContentService, mockLLMClient)
	
	// Test
	ctx := context.Background()
	result, err := composer.ComposeGuidance(ctx, "common_cold", "en")
	
	// Assertions
	if err != nil {
		t.Fatalf("Expected no error, got: %v", err)
	}
	
	if result == nil {
		t.Fatal("Expected guidance card, got nil")
	}
	
	// Verify LLM enhancement
	if len(result.SelfCare) == 0 {
		t.Error("Expected self-care recommendations")
	}
	
	// Check that LLM enhanced the content
	if result.SelfCare[0] != "Enhanced self-care recommendation 1 (structured by LLM)" {
		t.Error("Expected LLM-enhanced content")
	}
	
	if result.Disclaimer != "Enhanced disclaimer structured by LLM for better readability" {
		t.Error("Expected LLM-enhanced disclaimer")
	}
	
	t.Logf("✅ LLM-powered guidance composition working correctly")
	t.Logf("Topic: %s, Language: %s", result.TopicKey, result.Language)
	t.Logf("Enhanced Self-care: %v", result.SelfCare)
	t.Logf("Enhanced Disclaimer: %s", result.Disclaimer)
}
