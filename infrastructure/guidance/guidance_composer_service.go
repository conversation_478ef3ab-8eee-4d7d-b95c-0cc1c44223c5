package guidance

import (
	"context"
	"fmt"

	"github.com/RemedyMate/remedymate-backend/domain/entities"
	"github.com/RemedyMate/remedymate-backend/domain/interfaces"
)

type GuidanceComposerService struct {
	contentService interfaces.ContentService
	llmClient      interfaces.LLMClient
}

// NewGuidanceComposerService creates a new guidance composer service
func NewGuidanceComposerService(contentService interfaces.ContentService, llmClient interfaces.LLMClient) interfaces.GuidanceComposerService {
	return &GuidanceComposerService{
		contentService: contentService,
		llmClient:      llmClient,
	}
}

// ComposeGuidance composes a guidance card for a given topic and language
func (gcs *GuidanceComposerService) ComposeGuidance(ctx context.Context, topicKey, language string) (*entities.GuidanceCard, error) {
	content, err := gcs.contentService.GetContentByTopic(topicKey, language)
	if err != nil {
		return nil, fmt.Errorf("failed to get content for topic %s: %w", topicKey, err)
	}

	return gcs.ComposeFromBlocks(ctx, topicKey, language, *content)
}

// ComposeFromBlocks composes a guidance card from approved content blocks using LLM
func (gcs *GuidanceComposerService) ComposeFromBlocks(ctx context.Context, topicKey, language string, blocks entities.ContentTranslation) (*entities.GuidanceCard, error) {
	if topicKey == "" {
		return nil, fmt.Errorf("topic key cannot be empty")
	}

	if language != "en" && language != "am" {
		return nil, fmt.Errorf("unsupported language: %s", language)
	}

	// Use LLM to intelligently compose the guidance card from blocks
	guidanceCard, err := gcs.llmClient.ComposeGuidance(ctx, blocks, topicKey, language)
	if err != nil {
		return nil, fmt.Errorf("failed to compose guidance with LLM: %w", err)
	}

	// Ensure disclaimer is always present as a safety fallback
	if guidanceCard.Disclaimer == "" {
		guidanceCard.Disclaimer = gcs.getDefaultDisclaimer(language)
	}

	return guidanceCard, nil
}

// getDefaultDisclaimer returns the default medical disclaimer
func (gcs *GuidanceComposerService) getDefaultDisclaimer(language string) string {
	if language == "am" {
		return "ይህ አጠቃላይ መረጃ ነው፣ የሕክምና ምክር አይደለም። ምልክቶችዎ ከባድ ወይም ቆይተው ከሚታወቁ ከሆነ፣ እባክዎን የሕክምና ባለሙያ ይጠይቁ።"
	}
	return "This is general information, not medical advice. If your symptoms are severe or persistent, please consult a healthcare professional."
}
