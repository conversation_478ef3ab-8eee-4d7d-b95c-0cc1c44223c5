package guidance

import (
	"context"
	"fmt"
	"log"

	"github.com/RemedyMate/remedymate-backend/domain/entities"
	"github.com/RemedyMate/remedymate-backend/domain/interfaces"
)

type GuidanceComposerService struct {
	contentService interfaces.ContentService
}


// NewGuidanceComposerService creates a new guidance composer service
func NewGuidanceComposerService(contentService interfaces.ContentService) interfaces.GuidanceComposerService {
	return &GuidanceComposerService{
		contentService: contentService,
	}
}

// ComposeGuidance composes a guidance card for a given topic and language
func (gcs *GuidanceComposerService) ComposeGuidance(ctx context.Context, topicKey, language string) (*entities.GuidanceCard, error) {
	content, err := gcs.contentService.GetContentByTopic(topicKey, language)
	if err != nil {
		return nil, fmt.Errorf("failed to get content for topic %s: %w", topicKey, err)
	}

	return gcs.ComposeFromBlocks(topicKey, language, *content)
}

// ComposeFromBlocks composes a guidance card from approved content blocks
func (gcs *GuidanceComposerService) ComposeFromBlocks(topicKey, language string, content entities.ContentTranslation) (*entities.GuidanceCard, error) {

	if topicKey == "" {
		return nil, fmt.Errorf("topic key cannot be empty")
	}

	if language != "en" && language != "am" {
		return nil, fmt.Errorf("unsupported language: %s", language)
	}

	// Create guidance card from approved blocks
	card := &entities.GuidanceCard{
		TopicKey:      topicKey,
		Language:      language,
		SelfCare:      content.SelfCare,
		OTCCategories: content.OTCCategories,
		SeekCareIf:    content.SeekCareIf,
		Disclaimer:    content.Disclaimer,
		IsOffline:     false, // Will be set by caller if from cache
	}

	// Validate the composed card
	if err := gcs.validateGuidanceCard(card); err != nil {
		log.Panicln()
		return nil, fmt.Errorf("guidance card validation failed: %w", err)
	}

	// Ensure disclaimer is always present
	if card.Disclaimer == "" {
		card.Disclaimer = gcs.getDefaultDisclaimer(language)
	}

	return card, nil
}


// validateGuidanceCard validates that a guidance card meets safety requirements
func (gcs *GuidanceComposerService) validateGuidanceCard(card *entities.GuidanceCard) error {
	// Must have self-care recommendations
	if len(card.SelfCare) == 0 {
		return fmt.Errorf("guidance card must include self-care recommendations")
	}

	// Must have "when to seek care" information
	if len(card.SeekCareIf) == 0 {
		return fmt.Errorf("guidance card must include 'when to seek care' information")
	}

	// Must have disclaimer
	if card.Disclaimer == "" {
		return fmt.Errorf("guidance card must include medical disclaimer")
	}

	// Validate OTC categories don't contain dosing information
	for _, otc := range card.OTCCategories {
		if gcs.containsDosingInfo(otc.CategoryName) || gcs.containsDosingInfo(otc.SafetyNote) {
			return fmt.Errorf("OTC category contains prohibited dosing information: %s", otc.CategoryName)
		}
	}

	// Validate self-care doesn't contain medical advice
	for _, selfCare := range card.SelfCare {
		if gcs.containsMedicalAdvice(selfCare) {
			return fmt.Errorf("self-care contains prohibited medical advice: %s", selfCare)
		}
	}

	return nil
}

// containsMedicalAdvice checks if text contains prohibited medical advice
func (gcs *GuidanceComposerService) containsMedicalAdvice(text string) bool {
	prohibitedTerms := []string{
		"diagnose", "diagnosis", "you have", "this is", "treatment for",
		"cure", "prescription", "medical condition",
	}

	textLower := text
	for _, term := range prohibitedTerms {
		if contains(textLower, term) {
			return true
		}
	}

	return false
}

// getDefaultDisclaimer returns the default medical disclaimer
func (gcs *GuidanceComposerService) getDefaultDisclaimer(language string) string {
	if language == "am" {
		return "ይህ አጠቃላይ መረጃ ነው፣ የሕክምና ምክር አይደለም። ምልክቶችዎ ከባድ ወይም ቆይተው ከሚታወቁ ከሆነ፣ እባክዎን የሕክምና ባለሙያ ይጠይቁ።"
	}
	return "This is general information, not medical advice. If your symptoms are severe or persistent, please consult a healthcare professional."
}

// containsDosingInfo checks if text contains dosing information (prohibited)
func (gcs *GuidanceComposerService) containsDosingInfo(text string) bool {
	prohibitedTerms := []string{
		"mg", "ml", "tablets", "pills", "dose", "dosage", "take 2", "take one",
		"every 4 hours", "twice daily", "three times", "milligrams",
	}

	textLower := text
	for _, term := range prohibitedTerms {
		if contains(textLower, term) {
			return true
		}
	}

	return false
}

// Helper function to check if string contains substring (case-insensitive)
func contains(text, substr string) bool {
	// Simple case-insensitive contains check
	// In a real implementation, you might want to use strings.ToLower()
	// but avoiding imports for this example
	return len(text) >= len(substr) && findSubstring(text, substr)
}

// Simple substring search helper
func findSubstring(text, substr string) bool {
	if len(substr) == 0 {
		return true
	}
	if len(text) < len(substr) {
		return false
	}

	for i := 0; i <= len(text)-len(substr); i++ {
		match := true
		for j := 0; j < len(substr); j++ {
			if toLower(text[i+j]) != toLower(substr[j]) {
				match = false
				break
			}
		}
		if match {
			return true
		}
	}
	return false
}

// Simple toLower helper for ASCII characters
func toLower(c byte) byte {
	if c >= 'A' && c <= 'Z' {
		return c + 32
	}
	return c
}
