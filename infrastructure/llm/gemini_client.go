package llm

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"io"
	"log"
	"net/http"
	"strings"
	"time"

	"github.com/RemedyMate/remedymate-backend/domain/dto"
	"github.com/RemedyMate/remedymate-backend/domain/entities"
	"github.com/RemedyMate/remedymate-backend/domain/interfaces"
)

// GeminiClient implements LLMClient using Google's Gemini API
type GeminiClient struct {
	config     dto.LLMConfig
	httpClient *http.Client
	baseURL    string
}

// NewGeminiClient creates a new Gemini client
func NewGeminiClient(config dto.LLMConfig) interfaces.LLMClient {
	return &GeminiClient{
		config:     config,
		httpClient: &http.Client{Timeout: time.Duration(config.Timeout) * time.Second},
		baseURL:    "https://generativelanguage.googleapis.com/v1beta/models",
	}
}

// ClassifyTriage calls Gemini API for triage classification
func (g *GeminiClient) ClassifyTriage(ctx context.Context, prompt string) (string, error) {
	return g.callGemini(ctx, prompt)
}

// ComposeGuidance uses LLM to intelligently structure guidance cards from content blocks
func (g *GeminiClient) ComposeGuidance(ctx context.Context, blocks entities.ContentTranslation, topicKey, language string) (*entities.GuidanceCard, error) {
	prompt := g.buildCompositionPrompt(blocks, topicKey, language)

	response, err := g.callGemini(ctx, prompt)
	if err != nil {
		return nil, fmt.Errorf("LLM composition failed: %w", err)
	}

	// Parse the LLM response into a structured guidance card
	guidanceCard, err := g.parseCompositionResponse(response, topicKey, language)
	if err != nil {
		return nil, fmt.Errorf("failed to parse LLM response: %w", err)
	}

	return guidanceCard, nil
}

// callGemini makes the actual API call to Gemini
func (g *GeminiClient) callGemini(ctx context.Context, prompt string) (string, error) {
	// 1. Construct the request for Gemini's API format
	geminiReq := dto.GeminiRequest{
		Contents: []dto.GeminiContent{
			{
				Parts: []dto.GeminiPart{{Text: prompt}},
				Role:  "user",
			},
		},
		GenerationConfig: dto.GeminiGenConfig{
			Temperature:     g.config.Temperature,
			MaxOutputTokens: g.config.MaxTokens,
			TopP:            0.95,
		},
		SafetySettings: []dto.GeminiSafetySetting{
			{Category: "HARM_CATEGORY_DANGEROUS_CONTENT", Threshold: "BLOCK_MEDIUM_AND_ABOVE"},
			{Category: "HARM_CATEGORY_HARASSMENT", Threshold: "BLOCK_MEDIUM_AND_ABOVE"},
		},
	}

	jsonData, err := json.Marshal(geminiReq)
	if err != nil {
		return "", fmt.Errorf("failed to marshal request: %w", err)
	}

	// 2. Build the HTTP request
	modelURL := fmt.Sprintf("%s/%s:generateContent?key=%s", g.baseURL, g.config.Model, g.config.APIKey)
	req, err := http.NewRequestWithContext(ctx, "POST", modelURL, bytes.NewBuffer(jsonData))
	if err != nil {
		return "", fmt.Errorf("failed to create request: %w", err)
	}
	req.Header.Set("Content-Type", "application/json")

	// 3. Execute the request
	resp, err := g.httpClient.Do(req)
	if err != nil {
		return "", fmt.Errorf("API request failed: %w", err)
	}
	defer resp.Body.Close()

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return "", fmt.Errorf("failed to read response: %w", err)
	}

	if resp.StatusCode != http.StatusOK {
		return "", fmt.Errorf("API returned status %d: %s", resp.StatusCode, string(body))
	}

	// 4. Parse the Gemini response
	var geminiResp dto.GeminiResponse
	if err := json.Unmarshal(body, &geminiResp); err != nil {
		return "", fmt.Errorf("failed to parse response: %w", err)
	}

	if geminiResp.Error != nil {
		return "", fmt.Errorf("gemini API error: %s", geminiResp.Error.Message)
	}

	if len(geminiResp.Candidates) == 0 || len(geminiResp.Candidates[0].Content.Parts) == 0 {
		return "", fmt.Errorf("no response content returned from Gemini")
	}

	// 5. Return the generated text
	return geminiResp.Candidates[0].Content.Parts[0].Text, nil
}

// buildCompositionPrompt creates a prompt for the LLM to compose guidance cards naturally
func (g *GeminiClient) buildCompositionPrompt(blocks entities.ContentTranslation, topicKey, language string) string {
	var prompt strings.Builder
	if language == "am" {
		prompt.WriteString("እርስዎ የሕክምና መመሪያ ካርድ የሚያዘጋጅ ባለሙያ ነዎት። የተሰጡትን የይዘት ብሎኮች በተፈጥሮአዊ እና ተደራሽ መንገድ ያዋቅሩ።\n\n")
	} else {
		prompt.WriteString("You are a medical guidance card composition expert. Structure the given content blocks in a natural and accessible way.\n\n")
	}

	prompt.WriteString(fmt.Sprintf("Topic: %s\nLanguage: %s\n\n", topicKey, language))

	log.Println("Building composition prompt for topic:", topicKey, "language:", language, "blocks:", blocks)
	// Add content blocks
	if len(blocks.SelfCare) > 0 {
		if language == "am" {
			prompt.WriteString("የራስ እንክብካቤ ምክሮች:\n")
		} else {
			prompt.WriteString("Self-care recommendations:\n")
		}
		for _, item := range blocks.SelfCare {
			prompt.WriteString(fmt.Sprintf("- %s\n", item))
		}
		prompt.WriteString("\n")
	}

	if len(blocks.OTCCategories) > 0 {
		if language == "am" {
			prompt.WriteString("ያለ ማዘዣ የሚገኙ መድሃኒቶች:\n")
		} else {
			prompt.WriteString("Over-the-counter options:\n")
		}
		for _, otc := range blocks.OTCCategories {
			prompt.WriteString(fmt.Sprintf("- %s: %s\n", otc.CategoryName, otc.SafetyNote))
		}
		prompt.WriteString("\n")
	}

	if len(blocks.SeekCareIf) > 0 {
		if language == "am" {
			prompt.WriteString("የሕክምና እርዳታ መፈለግ ያለብዎት ጊዜ:\n")
		} else {
			prompt.WriteString("Seek medical care if:\n")
		}
		for _, item := range blocks.SeekCareIf {
			prompt.WriteString(fmt.Sprintf("- %s\n", item))
		}
		prompt.WriteString("\n")
	}

	if blocks.Disclaimer != "" {
		if language == "am" {
			prompt.WriteString("ማስተዋወቂያ:\n")
		} else {
			prompt.WriteString("Disclaimer:\n")
		}
		prompt.WriteString(blocks.Disclaimer)
		prompt.WriteString("\n\n")
	}

	// Instructions for the LLM
	if language == "am" {
		prompt.WriteString("እባክዎን ይህንን ይዘት በተፈጥሮአዊ፣ ተደራሽ እና ተጠቃሚ ተኮር መንገድ ያዋቅሩ። JSON ቅርጸት ይጠቀሙ።")
	} else {
		prompt.WriteString("Please structure this content in a natural, accessible, and user-friendly way. Use JSON format with the same structure as the input.")
	}

	return prompt.String()
}

// parseCompositionResponse parses the LLM response into a structured guidance card
func (g *GeminiClient) parseCompositionResponse(response, topicKey, language string) (*entities.GuidanceCard, error) {
	// Try to extract JSON from the response
	jsonStart := strings.Index(response, "{")
	jsonEnd := strings.LastIndex(response, "}")

	if jsonStart == -1 || jsonEnd == -1 || jsonEnd <= jsonStart {
		// If no JSON found, create a basic structure from the response
		return g.createFallbackGuidanceCard(response, topicKey, language), nil
	}

	jsonStr := response[jsonStart : jsonEnd+1]

	// Try to parse as ContentTranslation first
	var content entities.ContentTranslation
	if err := json.Unmarshal([]byte(jsonStr), &content); err == nil {
		// Successfully parsed, create guidance card
		return &entities.GuidanceCard{
			TopicKey:      topicKey,
			Language:      language,
			SelfCare:      content.SelfCare,
			OTCCategories: content.OTCCategories,
			SeekCareIf:    content.SeekCareIf,
			Disclaimer:    content.Disclaimer,
			IsOffline:     false,
		}, nil
	}

	// Try to parse directly as GuidanceCard
	var card entities.GuidanceCard
	if err := json.Unmarshal([]byte(jsonStr), &card); err == nil {
		card.TopicKey = topicKey
		card.Language = language
		card.IsOffline = false
		return &card, nil
	}

	// If JSON parsing fails, create fallback
	return g.createFallbackGuidanceCard(response, topicKey, language), nil
}

// createFallbackGuidanceCard creates a basic guidance card when JSON parsing fails
func (g *GeminiClient) createFallbackGuidanceCard(response, topicKey, language string) *entities.GuidanceCard {
	// Parse the response text to extract sections
	lines := strings.Split(response, "\n")
	var selfCare []string
	var seekCareIf []string
	var otcCategories []entities.OTCCategory
	disclaimer := ""

	currentSection := ""
	for _, line := range lines {
		line = strings.TrimSpace(line)
		if line == "" {
			continue
		}

		// Detect section headers
		lowerLine := strings.ToLower(line)
		if strings.Contains(lowerLine, "self-care") || strings.Contains(lowerLine, "እንክብካቤ") {
			currentSection = "selfcare"
			continue
		} else if strings.Contains(lowerLine, "seek") || strings.Contains(lowerLine, "medical") || strings.Contains(lowerLine, "ሕክምና") {
			currentSection = "seekcare"
			continue
		} else if strings.Contains(lowerLine, "disclaimer") || strings.Contains(lowerLine, "ማስተዋወቂያ") {
			currentSection = "disclaimer"
			continue
		}

		// Add content to appropriate section
		if strings.HasPrefix(line, "-") || strings.HasPrefix(line, "•") {
			content := strings.TrimSpace(line[1:])
			if currentSection == "selfcare" {
				selfCare = append(selfCare, content)
			} else if currentSection == "seekcare" {
				seekCareIf = append(seekCareIf, content)
			}
		} else if currentSection == "disclaimer" {
			disclaimer += line + " "
		}
	}

	return &entities.GuidanceCard{
		TopicKey:      topicKey,
		Language:      language,
		SelfCare:      selfCare,
		OTCCategories: otcCategories,
		SeekCareIf:    seekCareIf,
		Disclaimer:    strings.TrimSpace(disclaimer),
		IsOffline:     false,
	}
}
