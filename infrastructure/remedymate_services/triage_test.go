package remedymate_services

import (
	"testing"

	"remedymate-backend/infrastructure/content"
)

// TestFormatRedFlagRulesForPrompt runs the method locally without the HTTP endpoint.
func TestFormatRedFlagRulesForPrompt(t *testing.T) {
    // adjust path relative to this package so ContentService can find data/
    cs := content.NewContentService("../../data")
    if cs == nil {
        t.Fatal("failed to create content service")
    }

    // construct the TriageService directly (avoid calling a missing constructor)
    ts := &TriageService{
        contentService: cs,
        llmClient:      nil, // nil is fine for this test if method doesn't use llmClient
    }

    out := ts.formatRedFlagRulesForPrompt("en")
    t.Logf("formatRedFlagRulesForPrompt output:\n%s", out)
}

func TestFormatYellowFlagRulesForPrompt(t *testing.T) {
    // adjust path relative to this package so ContentService can find data/
    cs := content.NewContentService("../../data")
    if cs == nil {
        t.Fatal("failed to create content service")
    }

    // construct the TriageService directly (avoid calling a missing constructor)
    ts := &TriageService{
        contentService: cs,
        llmClient:      nil, // nil is fine for this test if method doesn't use llmClient
    }

    out := ts.formatYellowFlagRulesForPrompt("en")
    t.Logf("formatYellowFlagRulesForPrompt output:\n%s", out)
}
