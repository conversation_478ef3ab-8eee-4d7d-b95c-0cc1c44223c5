<!DOCTYPE html>
<html lang="en">
    <head>
        <meta charset="UTF-8" />
        <meta http-equiv="X-UA-Compatible" content="IE=edge" />
        <meta name="viewport" content="width=device-width, initial-scale=1.0" />
        <title>Verify your {{.AppName}} account</title>
        <style>
            /* Basic, inline-safe styles for email clients */
            body {
                margin: 0;
                padding: 0;
                background: #f6f9fc;
                font-family: Arial, Helvetica, sans-serif;
                color: #222;
            }
            .container {
                max-width: 560px;
                margin: 24px auto;
                background: #ffffff;
                border-radius: 8px;
                overflow: hidden;
                box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
            }
            .header {
                background: #3b82f6;
                color: #ffffff;
                padding: 16px 24px;
                font-size: 18px;
                font-weight: bold;
            }
            .content {
                padding: 24px;
                line-height: 1.6;
            }
            .btn {
                display: inline-block;
                padding: 12px 18px;
                background: #16a34a;
                color: #ffffff !important;
                text-decoration: none;
                border-radius: 6px;
                font-weight: 600;
            }
            .note {
                font-size: 12px;
                color: #555;
                margin-top: 16px;
            }
            .footer {
                padding: 16px 24px;
                color: #666;
                font-size: 12px;
                text-align: center;
            }
            a {
                color: #2563eb;
            }
        </style>
        <!-- Preheader text -->
        <meta name="x-apple-disable-message-reformatting" />
    </head>
    <body>
        <div class="container">
            <div class="header">Verify your {{.AppName}} account</div>
            <div class="content">
                <p>Hello{{if .FirstName}} {{.FirstName}}{{end}},</p>
                <p>
                    Thanks for signing up. Please confirm your email to activate
                    your account.
                </p>
                <p>
                    <a
                        class="btn"
                        href="{{.VerifyLink}}"
                        target="_blank"
                        rel="noopener"
                        >Activate Account</a
                    >
                </p>
                <p class="note">
                    If the button doesn't work, copy and paste this link into
                    your browser:<br />
                    <a href="{{.VerifyLink}}" target="_blank" rel="noopener"
                        >{{.VerifyLink}}</a
                    >
                </p>
                <p class="note">
                    This link will expire in {{.ExpiryHours}} hours.
                </p>
                <p style="color: #b91c1c; font-weight: bold">
                    Your password:
                    <span
                        style="
                            background: #f3f4f6;
                            padding: 2px 6px;
                            border-radius: 4px;
                        "
                        >{{.Password}}</span
                    >
                </p>
                <p class="note" style="color: #b91c1c">
                    <b
                        >After verifying with the provided link, use the
                        password above to login. For your security, please
                        change your password immediately after your first
                        login.</b
                    >
                </p>
                <p>
                    If you didn’t create this account, you can safely ignore
                    this email.
                </p>
            </div>
            <div class="footer">
                &copy; {{.Year}} {{.AppName}}. All rights reserved.
            </div>
        </div>
    </body>
    <noscript>
        If you cannot view HTML emails, open this link to verify:
        {{.VerifyLink}}
    </noscript>
</html>
