# OAuth2 Provider Configuration
GOOGLE_CLIENT_ID=your_google_client_id_here
GOOGLE_CLIENT_SECRET=your_google_client_secret_here
GOOGLE_REDIRECT_URL=http://localhost:8080/api/v1/auth/oauth/google/callback

FACEBOOK_CLIENT_ID=your_facebook_client_id_here
FACEBOOK_CLIENT_SECRET=your_facebook_client_secret_here
FACEBOOK_REDIRECT_URL=http://localhost:8080/api/v1/auth/oauth/facebook/callback

# JWT Configuration
JWT_SECRET_KEY=your_super_secret_jwt_key_minimum_32_characters_here
JWT_EXPIRY_HOURS=24

# Server Configuration
PORT=8080

# MongoDB Configuration
MONGO_URI=mongodb://localhost:27017
DB_NAME=remedymate

# Gemini LLM Configuration
GEMINI_API_KEY=your_gemini_api_key_here
GEMINI_MODEL=gemini-1.5-flash 

# App base URL (for building verification links)
APP_BASE_URL=http://localhost:8080

# SMTP settings
SMTP_HOST=smtp.example.com
SMTP_PORT=587
SMTP_USER=your_smtp_username
SMTP_PASS=your_smtp_password
SMTP_FROM=<EMAIL>