root = "."
tmp_dir = "tmp"

[build]
# Just plain old shell command.
cmd = "go build -o ./tmp/main ./delivery/main.go"
# Binary file yields from `cmd`.
bin = "./tmp/main"
# Customize binary: export terminal/color env vars so child process emits ANSI colors
# (air runs the app as a subprocess, so we force the env to a color-capable terminal)
full_bin = "TERM=xterm-256color COLORTERM=truecolor FORCE_COLOR=1 GIN_MODE=debug ./tmp/main"
# Add additional arguments when running binary (bin/full_bin)
args_bin = []
# Watch these filename extensions.
include_ext = ["go"]
# Ignore these filename extensions or directories.
exclude_dir = ["assets", "tmp", "vendor"]
# Exclude unchanged files.
exclude_unchanged = true
# Follow symlink for directories.
follow_symlink = true
# This log file is placed in your tmp_dir.
log = "air.log"
# Poll files for changes instead of using fsnotify.
poll = false
# Poll interval (defaults to the minimum interval of 500ms).
poll_interval = 500 # ms
# It's not necessary to trigger build each time file changes if it's too frequent.
delay = 0 # ms
# Stop running old binary when build errors occur.
stop_on_error = true
# Send Interrupt signal before killing process (windows does not support this feature).
send_interrupt = false
# Delay after sending Interrupt signal.
kill_delay = 500 # nanosecond
# Rerun binary or not.
rerun = false
# Delay after each execution.
rerun_delay = 500

[log]
# Show log time.
time = false
# Only show main log (silences watcher, build, runner).
main_only = false
# Silence all logs produced by air.
silent = false

[color]
# Customize each part's color. If no color found, use the raw app log.
main = "magenta"
watcher = "cyan"
build = "yellow"
runner = "green"

[misc]
# Delete tmp directory on exit.
clean_on_exit = true

[screen]
clear_on_rebuild = true
keep_scroll = true

[proxy]
# Enable live-reloading on the browser.
enabled = true
proxy_port = 8090
app_port = 8080