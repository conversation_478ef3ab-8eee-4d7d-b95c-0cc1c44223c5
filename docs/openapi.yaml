openapi: 3.0.0
info:
    title: RemedyMate Backend API
    version: 1.0.0
    description: |
        Spec-first OpenAPI v3 definition for RemedyMate.
        - Auth: registration, login, refresh, verify, activate, logout, change password
        - Users: profile read/update/delete
        - Conversation: start/continue unified endpoint and offline topics
        - Remedy: triage + map + compose
        - Admin: topics, red flags, feedback management

servers:
    - url: http://localhost:8080
      description: Local dev server
    - url: https://{host}
      description: Deployed server
      variables:
          host:
              default: api.example.com

tags:
    - name: Auth
      description: Authentication and account lifecycle
    - name: Users
      description: User profile management
    - name: Admin/Users
      description: Admin user management (superadmin only)
    - name: Conversation
      description: Symptom conversation flow (public)
    - name: Remedy
      description: Symptom triage, topic mapping, and content composition
    - name: Topics
      description: Admin topic management
    - name: Admin/RedFlags
      description: Admin red flag rules management
    - name: Admin/Feedback
      description: Admin feedback management
    - name: Feedback
      description: Public feedback endpoint

components:
    securitySchemes:
        bearerAuth:
            type: http
            scheme: bearer
            bearerFormat: JWT

    responses:
        Unauthorized:
            description: Unauthorized
            content:
                application/json:
                    schema:
                        $ref: "#/components/schemas/ErrorResponse"
        Forbidden:
            description: Forbidden
            content:
                application/json:
                    schema:
                        $ref: "#/components/schemas/ErrorResponse"
        NotFound:
            description: Resource not found
            content:
                application/json:
                    schema:
                        $ref: "#/components/schemas/ErrorResponse"

    schemas:
        ErrorResponse:
            type: object
            properties:
                error:
                    type: string
                code:
                    type: string
                details:
                    type: string

        # ===== Auth DTOs =====
        PersonalInfoDTO:
            type: object
            properties:
                firstName:
                    type: string
                lastName:
                    type: string
                age:
                    type: integer
                    format: int32
                gender:
                    type: string
                profilePictureUrl:
                    type: string
                    format: uri
            additionalProperties: false

        RegisterDTO:
            type: object
            required: [email, role, personalInfo, frontendDomain]
            properties:
                username:
                    type: string
                    description: Optional. If not provided, will be generated.
                email:
                    type: string
                    format: email
                password:
                    type: string
                    format: password
                    minLength: 6
                    description: Optional. If not provided, will be generated and sent in verification email.
                role:
                    type: string
                    enum: [admin, superadmin]
                personalInfo:
                    $ref: "#/components/schemas/PersonalInfoDTO"
                frontendDomain:
                    type: string
                    format: uri
                    description: Frontend web domain (e.g. https://app.example.com) used for verification link.

        RegisterResponseDTO:
            type: object
            properties:
                message:
                    type: string
            example:
                message: User registered successfully

        LoginDTO:
            type: object
            required: [email, password]
            properties:
                email:
                    type: string
                    format: email
                password:
                    type: string
                    format: password

        LoginResponseDTO:
            type: object
            properties:
                access_token:
                    type: string
                refresh_token:
                    type: string
                user_id:
                    type: string
                username:
                    type: string
                email:
                    type: string
                    format: email
                role:
                    type: string
                    enum: [admin, superadmin]
                message:
                    type: string
                    description: Optional. Present on first login to remind user to change password.
            example:
                access_token: eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
                refresh_token: eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
                user_id: 64f2c7f8a1b2c3d4e5f6a7b8
                username: johndoe
                email: <EMAIL>
                role: admin
                message: Welcome! For your security, please change your password now.
        ResendVerificationDTO:
            type: object
            required: [email, frontendDomain]
            properties:
                email:
                    type: string
                    format: email
                frontendDomain:
                    type: string
                    format: uri
                    description: Frontend web domain (e.g. https://app.example.com) used for verification link.

        ResendVerificationResponseDTO:
            type: object
            properties:
                message:
                    type: string
            example:
                message: Verification email resent if the account exists.

        RefreshDTO:
            type: object
            required: [refresh_token]
            properties:
                refresh_token:
                    type: string

        RefreshResponseDTO:
            type: object
            properties:
                access_token:
                    type: string
                refresh_token:
                    type: string
            example:
                access_token: eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
                refresh_token: eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...

        ActivateDTO:
            type: object
            required: [email]
            properties:
                email:
                    type: string
                    format: email

        ActivateResponseDTO:
            type: object
            properties:
                message:
                    type: string
            example:
                message: Account activated

        ChangePasswordRequest:
            type: object
            required: [old_password, new_password]
            properties:
                old_password:
                    type: string
                new_password:
                    type: string
                    minLength: 6
            example:
                old_password: OldP@ssw0rd
                new_password: NewP@ssw0rd123

        # ===== User Profile DTOs =====
        ProfileResponseDTO:
            type: object
            properties:
                id:
                    type: string
                username:
                    type: string
                email:
                    type: string
                    format: email
                personalInfo:
                    $ref: "#/components/schemas/PersonalInfoDTO"
                isVerified:
                    type: boolean
                isProfileFull:
                    type: boolean
                isActive:
                    type: boolean
                createdAt:
                    type: string
                    format: date-time
                updatedAt:
                    type: string
                    format: date-time
                lastLogin:
                    type: string
                    format: date-time

        UpdateProfileDTO:
            type: object
            properties:
                username:
                    type: string
                personalInfo:
                    $ref: "#/components/schemas/PersonalInfoDTO"

        DeleteProfileDTO:
            type: object
            required: [password]
            properties:
                password:
                    type: string
                reason:
                    type: string

        # ===== Admin User Management DTOs =====
        PaginationMetadata:
            type: object
            properties:
                page:
                    type: integer
                    description: Current page number
                limit:
                    type: integer
                    description: Number of items per page
                total:
                    type: integer
                    format: int64
                    description: Total number of items
                total_pages:
                    type: integer
                    description: Total number of pages
                has_next:
                    type: boolean
                    description: Whether there's a next page
                has_prev:
                    type: boolean
                    description: Whether there's a previous page
            example:
                page: 1
                limit: 10
                total: 25
                total_pages: 3
                has_next: true
                has_prev: false

        PaginatedResponse:
            type: object
            properties:
                data:
                    type: array
                    items: {}
                    description: Array of data items
                pagination:
                    $ref: "#/components/schemas/PaginationMetadata"
                message:
                    type: string
            example:
                message: "Data retrieved successfully"
                pagination:
                    page: 1
                    limit: 10
                    total: 25
                    total_pages: 3
                    has_next: true
                    has_prev: false
                data: []

        # ===== Conversation DTOs =====
        Question:
            type: object
            properties:
                id:
                    type: integer
                    format: int32
                text:
                    type: string
                type:
                    type: string
                    description: duration | location | severity | history | triggers
                required:
                    type: boolean

        ConversationRequest:
            type: object
            properties:
                conversation_id:
                    type: string
                    description: Required for continuing, optional for starting
                symptom:
                    type: string
                language:
                    type: string
                    enum: [en, am]
                answer:
                    type: string
                user_id:
                    type: string

        HealthReport:
            type: object
            properties:
                symptom:
                    type: string
                duration:
                    type: string
                location:
                    type: string
                severity:
                    type: string
                associated_symptoms:
                    type: array
                    items:
                        type: string
                medical_history:
                    type: string
                triggers:
                    type: string
                possible_conditions:
                    type: array
                    items:
                        type: string
                recommendations:
                    type: array
                    items:
                        type: string
                urgency_level:
                    type: string
                generated_at:
                    type: string
                    format: date-time
                remedy:
                    $ref: "#/components/schemas/Remedy"

        ConversationResponse:
            type: object
            properties:
                conversation_id:
                    type: string
                heading:
                    type: string
                subheading:
                    type: string
                question:
                    $ref: "#/components/schemas/Question"
                message:
                    type: string
                is_complete:
                    type: boolean
                current_step:
                    type: integer
                total_steps:
                    type: integer
                report:
                    $ref: "#/components/schemas/HealthReport"
                remedy:
                    $ref: "#/components/schemas/RemedyResponse"
                is_new_conversation:
                    type: boolean

        # ===== Remedy DTOs =====
        TriageLevel:
            type: string
            enum: [GREEN, YELLOW, RED]

        TriageResponse:
            type: object
            properties:
                level:
                    $ref: "#/components/schemas/TriageLevel"
                red_flags:
                    type: array
                    items:
                        type: string
                message:
                    type: string
                session_id:
                    type: string

        OTCCategory:
            type: object
            properties:
                category_name:
                    type: string
                safety_note:
                    type: string

        GuidanceCard:
            type: object
            properties:
                topic_key:
                    type: string
                language:
                    type: string
                    enum: [en, am]
                self_care:
                    type: array
                    items:
                        type: string
                otc_categories:
                    type: array
                    items:
                        $ref: "#/components/schemas/OTCCategory"
                seek_care_if:
                    type: array
                    items:
                        type: string
                disclaimer:
                    type: string
                is_offline:
                    type: boolean

        RemedyRequest:
            type: object
            required: [text, language]
            properties:
                text:
                    type: string
                    minLength: 3
                    maxLength: 500
                language:
                    type: string
                    enum: [en, am]

        Remedy:
            type: object
            properties:
                triage:
                    $ref: "#/components/schemas/TriageResponse"
                self_care:
                    type: array
                    items:
                        type: string
                otc_categories:
                    type: array
                    items:
                        $ref: "#/components/schemas/OTCCategory"
                seek_care_if:
                    type: array
                    items:
                        type: string
                disclaimer:
                    type: string
                topic_key:
                    type: string
                language:
                    type: string
                    enum: [en, am]

        RemedyResponse:
            type: object
            properties:
                session_id:
                    type: string
                triage:
                    $ref: "#/components/schemas/TriageResponse"
                guidance_card:
                    $ref: "#/components/schemas/GuidanceCard"

        # ===== Topics =====
        LocalizedGuidanceContent:
            type: object
            properties:
                self_care:
                    type: array
                    items:
                        type: string
                otc_categories:
                    type: array
                    items:
                        $ref: "#/components/schemas/OTCCategory"
                seek_care_if:
                    type: array
                    items:
                        type: string
                disclaimer:
                    type: string

        TopicStatus:
            type: string
            enum: [active, deleted]

        Topic:
            type: object
            properties:
                id:
                    type: string
                topic_key:
                    type: string
                name_en:
                    type: string
                name_am:
                    type: string
                description_en:
                    type: string
                description_am:
                    type: string
                status:
                    $ref: "#/components/schemas/TopicStatus"
                translations:
                    type: object
                    properties:
                        en:
                            $ref: "#/components/schemas/LocalizedGuidanceContent"
                        am:
                            $ref: "#/components/schemas/LocalizedGuidanceContent"
                    required:
                        - en
                        - am
                    additionalProperties: false
                version:
                    type: integer
                revision_history:
                    type: array
                    items:
                        type: object
                created_at:
                    type: string
                    format: date-time
                updated_at:
                    type: string
                    format: date-time
                created_by:
                    type: string
                updated_by:
                    type: string

        TopicCreateRequest:
            type: object
            required:
                [topic_key, name_en, name_am, is_offline_cachable, translations]
            properties:
                topic_key:
                    type: string
                name_en:
                    type: string
                name_am:
                    type: string
                description_en:
                    type: string
                description_am:
                    type: string
                is_offline_cachable:
                    type: boolean
                translations:
                    type: object
                    properties:
                        en:
                            $ref: "#/components/schemas/LocalizedGuidanceContent"
                        am:
                            $ref: "#/components/schemas/LocalizedGuidanceContent"
                    required:
                        - en
                        - am
                    additionalProperties: false
                    example:
                        en:
                            self_care:
                            - "Get plenty of rest."
                            otc_categories:
                                - category_name: "Fever reducer / pain reliever"
                            safety_note: "Follow dosage instructions"
                            seek_care_if:
                                - "Your fever is 39°C (102°F) or higher."
                            disclaimer: "This is not medical advice."
                        am:
                            self_care:
                                - "በቂ እረፍት ያግኙ።"
                            otc_categories:
                                - category_name: "ትኩሳት መቀነሻ"
                            safety_note: "የአጠቃቀም መመሪያ ይከተሉ።"
                            seek_care_if:
                                - "ትኩሳቱ ከ3 ቀናት በላይ ከቆየ።"
                            disclaimer: "ይህ አጠቃላይ መረጃ ነው።"

        TopicUpdateRequest:
            type: object
            properties:
                name_en:
                    type: string
                name_am:
                    type: string
                description_en:
                    type: string
                description_am:
                    type: string
                is_offline_cachable:
                    type: boolean
                status:
                    $ref: "#/components/schemas/TopicStatus"
                translations:
                    type: object
                    properties:
                        en:
                            $ref: "#/components/schemas/LocalizedGuidanceContent"
                        am:
                            $ref: "#/components/schemas/LocalizedGuidanceContent"
                    required:
                        - en
                        - am
                    additionalProperties: false

        PaginatedTopicsResult:
            type: object
            properties:
                topics:
                    type: array
                    items:
                        $ref: "#/components/schemas/Topic"
                total_count:
                    type: integer
                    format: int64
                page:
                    type: integer
                limit:
                    type: integer

        # ===== Public Feedback =====
        CreateFeedbackDTO:
            type: object
            required: [sessionId, topicKey, language, rating]
            properties:
                sessionId:
                    type: string
                topicKey:
                    type: string
                language:
                    type: string
                    enum: [en, am]
                rating:
                    type: integer
                    minimum: 1
                    maximum: 5
                message:
                    type: string

        Feedback:
            type: object
            properties:
                id:
                    type: string
                sessionId:
                    type: string
                topicKey:
                    type: string
                language:
                    type: string
                    enum: [en, am]
                rating:
                    type: integer
                message:
                    type: string
                createdAt:
                    type: string
                    format: date-time

        AdminFeedbackListResponse:
            type: object
            properties:
                items:
                    type: array
                    items:
                        $ref: "#/components/schemas/Feedback"
                total:
                    type: integer

        # ===== Admin RedFlags =====
        RedFlag:
            type: object
            properties:
                id:
                    type: string
                keywords:
                    type: array
                    items:
                        type: string
                language:
                    type: string
                    enum: [en, am]
                level:
                    $ref: "#/components/schemas/TriageLevel"
                description:
                    type: string
                createdAt:
                    type: string
                    format: date-time
                updatedAt:
                    type: string
                    format: date-time

        CreateRedFlagDTO:
            type: object
            required: [keywords, language, level, description]
            properties:
                keywords:
                    type: array
                    minItems: 1
                    items:
                        type: string
                language:
                    type: string
                    enum: [en, am]
                level:
                    type: string
                    enum: [RED, YELLOW]
                description:
                    type: string
                    minLength: 3

        UpdateRedFlagDTO:
            type: object
            properties:
                keywords:
                    type: array
                    minItems: 1
                    items:
                        type: string
                language:
                    type: string
                    enum: [en, am]
                level:
                    type: string
                    enum: [RED, YELLOW]
                description:
                    type: string

        # ===== Content Health Topics (public offline) =====
        HealthTopic:
            type: object
            properties:
                _id:
                    type: string
                topic_key:
                    type: string
                name_en:
                    type: string
                name_am:
                    type: string
                description_en:
                    type: string
                description_am:
                    type: string
                status:
                    type: string
                translations:
                    type: object
                    properties:
                        en:
                            type: object
                            properties:
                                self_care:
                                    type: array
                                    items:
                                        type: string
                                otc_categories:
                                    type: array
                                    items:
                                        $ref: "#/components/schemas/OTCCategory"
                                seek_care_if:
                                    type: array
                                    items:
                                        type: string
                                disclaimer:
                                    type: string
                        am:
                            type: object
                            properties:
                                self_care:
                                    type: array
                                    items:
                                        type: string
                                otc_categories:
                                    type: array
                                    items:
                                        $ref: "#/components/schemas/OTCCategory"
                                seek_care_if:
                                    type: array
                                    items:
                                        type: string
                                disclaimer:
                                    type: string
                version:
                    type: integer
                created_at:
                    type: string
                    format: date-time
                updated_at:
                    type: string
                    format: date-time
                created_by:
                    type: string
                updated_by:
                    type: string

paths:
    /api/v1/register:
        post:
            tags: [Auth]
            summary: Register a new user (admin-only)
            operationId: authRegisterAdmin
            description: Creates a new user account and sends an activation email with a verification link. NOTE; role provided is currently ignored by the server and defaults to admin. but restricted to superadmin via JWT.
            security:
                - bearerAuth: []
            requestBody:
                required: true
                content:
                    application/json:
                        schema:
                            $ref: "#/components/schemas/RegisterDTO"

            responses:
                "201":
                    description: Created
                    content:
                        application/json:
                            schema:
                                $ref: "#/components/schemas/RegisterResponseDTO"
                "400":
                    description: Bad Request
                    content:
                        application/json:
                            schema:
                                $ref: "#/components/schemas/ErrorResponse"
                            examples:
                                invalid_body:
                                    value:
                                        error: "Invalid request body"
                                user_exists:
                                    value:
                                        error: "User with this email already exists"
                                validation_error:
                                    value:
                                        error: "Key: 'RegisterDTO.Email' Error:Field validation for 'Email' failed on the 'required' tag"
                "401": { $ref: "#/components/responses/Unauthorized" }
                "403": { $ref: "#/components/responses/Forbidden" }

    /api/v1/auth/login:
        post:
            tags: [Auth]
            summary: Login
            operationId: authLogin
            requestBody:
                required: true
                content:
                    application/json:
                        schema:
                            $ref: "#/components/schemas/LoginDTO"
                        examples:
                            sample:
                                value:
                                    email: <EMAIL>
                                    password: MyS3cret!
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: "#/components/schemas/LoginResponseDTO"
                "400":
                    description: Bad Request
                    content:
                        application/json:
                            schema:
                                $ref: "#/components/schemas/ErrorResponse"
                            examples:
                                invalid_body:
                                    value:
                                        error: "Invalid request body"
                                validation_error:
                                    value:
                                        error: "Key: 'LoginDTO.Email' Error:Field validation for 'Email' failed on the 'required' tag"
                "401":
                    description: Unauthorized
                    content:
                        application/json:
                            schema:
                                type: object
                                properties:
                                    error:
                                        type: string
                            example:
                                error: "invalid credentials"

    /api/v1/auth/refresh:
        post:
            tags: [Auth]
            summary: Refresh tokens
            operationId: authRefresh
            requestBody:
                required: true
                content:
                    application/json:
                        schema:
                            $ref: "#/components/schemas/RefreshDTO"
                        examples:
                            sample:
                                value:
                                    refresh_token: eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: "#/components/schemas/RefreshResponseDTO"
                "400":
                    description: Bad Request
                    content:
                        application/json:
                            schema:
                                $ref: "#/components/schemas/ErrorResponse"
                            examples:
                                invalid_body:
                                    value:
                                        error: "Invalid request body"
                                invalid_token:
                                    value:
                                        error: "Invalid refresh token"

    /api/v1/auth/activate:
        post:
            tags: [Auth]
            summary: Activate account by email
            operationId: authActivate
            requestBody:
                required: true
                content:
                    application/json:
                        schema:
                            $ref: "#/components/schemas/ActivateDTO"
                        examples:
                            sample:
                                value:
                                    email: <EMAIL>
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: "#/components/schemas/ActivateResponseDTO"
                            examples:
                                success:
                                    value:
                                        message: Account activated
                "400":
                    description: Bad Request
                    content:
                        application/json:
                            schema:
                                $ref: "#/components/schemas/ErrorResponse"
                            examples:
                                invalid_body:
                                    value:
                                        error: "Invalid request body"
                                not_found:
                                    value:
                                        error: "User not found"
                                invalid_request:
                                    value:
                                        error: "Invalid activation request"

    /api/v1/auth/verify:
        get:
            tags: [Auth]
            summary: Verify account via token
            operationId: authVerify
            parameters:
                - in: query
                  name: token
                  schema:
                      type: string
                  required: true
                  description: Verification token sent by email
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                type: object
                                properties:
                                    message:
                                        type: string
                                example:
                                    message: Account verified
                "400":
                    description: Bad Request
                    content:
                        application/json:
                            schema:
                                $ref: "#/components/schemas/ErrorResponse"
                            examples:
                                missing_token:
                                    value:
                                        error: "token is required"
                                invalid_token:
                                    value:
                                        error: "Invalid or expired token"
    /api/v1/auth/resend-verification:
        post:
            tags: [Auth]
            summary: Resend verification email
            operationId: authResendVerification
            description: Resends the verification email with a new or existing token if the account exists and is not yet verified.
            requestBody:
                required: true
                content:
                    application/json:
                        schema:
                            $ref: "#/components/schemas/ResendVerificationDTO"
                        examples:
                            sample:
                                value:
                                    email: <EMAIL>
                                    frontendDomain: https://app.example.com
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: "#/components/schemas/ResendVerificationResponseDTO"
                "400":
                    description: Bad Request
                    content:
                        application/json:
                            schema:
                                $ref: "#/components/schemas/ErrorResponse"

    /api/v1/auth/logout:
        post:
            tags: [Auth]
            summary: Logout current user
            operationId: authLogout
            security:
                - bearerAuth: []
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                type: object
                                properties:
                                    message:
                                        type: string
                            examples:
                                success:
                                    value:
                                        message: Logout successful
                "401": { $ref: "#/components/responses/Unauthorized" }
                "500":
                    description: Internal Server Error
                    content:
                        application/json:
                            schema:
                                $ref: "#/components/schemas/ErrorResponse"
                            example:
                                error: "Logout failed"

    /api/v1/auth/change-password:
        post:
            tags: [Auth]
            summary: Change password
            operationId: authChangePassword
            security:
                - bearerAuth: []
            requestBody:
                required: true
                content:
                    application/json:
                        schema:
                            $ref: "#/components/schemas/ChangePasswordRequest"
                        examples:
                            sample:
                                value:
                                    old_password: OldP@ssw0rd
                                    new_password: NewP@ssw0rd123
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                type: object
                                properties:
                                    message:
                                        type: string
                            examples:
                                success:
                                    value:
                                        message: Password changed successfully
                "400":
                    description: Bad Request
                    content:
                        application/json:
                            schema:
                                $ref: "#/components/schemas/ErrorResponse"
                            examples:
                                invalid_body:
                                    value:
                                        error: "Invalid request body"
                                incorrect_password:
                                    value:
                                        error: "Incorrect old password"
                "401": { $ref: "#/components/responses/Unauthorized" }

    /api/v1/users/profile:
        get:
            tags: [Users]
            summary: Get current user's profile
            security:
                - bearerAuth: []
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                type: object
                                properties:
                                    message:
                                        type: string
                                    profile:
                                        $ref: "#/components/schemas/ProfileResponseDTO"
                "401": { $ref: "#/components/responses/Unauthorized" }
        put:
            tags: [Users]
            summary: Update current user's profile
            security:
                - bearerAuth: []
            requestBody:
                required: true
                content:
                    application/json:
                        schema:
                            $ref: "#/components/schemas/UpdateProfileDTO"
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                type: object
                                properties:
                                    message:
                                        type: string
                                    profile:
                                        $ref: "#/components/schemas/ProfileResponseDTO"
                "400":
                    description: Bad Request
                    content:
                        application/json:
                            schema:
                                $ref: "#/components/schemas/ErrorResponse"
                "401": { $ref: "#/components/responses/Unauthorized" }
        delete:
            tags: [Users]
            summary: Delete current user's profile (soft delete)
            description: Requires password in the body.
            security:
                - bearerAuth: []
            requestBody:
                required: true
                content:
                    application/json:
                        schema:
                            $ref: "#/components/schemas/DeleteProfileDTO"
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                type: object
                                properties:
                                    message:
                                        type: string
                "400":
                    description: Bad Request
                    content:
                        application/json:
                            schema:
                                $ref: "#/components/schemas/ErrorResponse"
                "401": { $ref: "#/components/responses/Unauthorized" }

    /api/v1/admin/users/profiles/paginated:
        get:
            tags: [Admin/Users]
            summary: Get user profiles with pagination, filtering, and sorting (superadmin only)
            description: |
                Retrieves user profiles with pagination support, including filtering by status and role,
                searching by username/email/name, and sorting capabilities.
                Only accessible by users with superadmin role.
            operationId: adminGetUserProfilesPaginated
            security:
                - bearerAuth: []
            parameters:
                - name: page
                  in: query
                  description: Page number (1-indexed)
                  required: false
                  schema:
                      type: integer
                      minimum: 1
                      default: 1
                  example: 1
                - name: limit
                  in: query
                  description: Number of items per page (max 100)
                  required: false
                  schema:
                      type: integer
                      minimum: 1
                      maximum: 100
                      default: 10
                  example: 10
                - name: search
                  in: query
                  description: Search by username, email, first name, or last name
                  required: false
                  schema:
                      type: string
                  example: "john"
                - name: status
                  in: query
                  description: Filter by user status
                  required: false
                  schema:
                      type: string
                      enum: [active, inactive, verified, unverified, all]
                      default: all
                  example: "active"
                - name: role
                  in: query
                  description: Filter by user role
                  required: false
                  schema:
                      type: string
                      enum: [admin, superadmin, all]
                      default: all
                  example: "admin"
                - name: sort_by
                  in: query
                  description: Sort by field
                  required: false
                  schema:
                      type: string
                      enum: [username, email, created_at, last_login]
                      default: created_at
                  example: "username"
                - name: order
                  in: query
                  description: Sort order
                  required: false
                  schema:
                      type: string
                      enum: [asc, desc]
                      default: desc
                  example: "asc"
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: "#/components/schemas/PaginatedResponse"
                "401": { $ref: "#/components/responses/Unauthorized" }
                "403": { $ref: "#/components/responses/Forbidden" }
                "500":
                    description: Internal Server Error
                    content:
                        application/json:
                            schema:
                                $ref: "#/components/schemas/ErrorResponse"
                            example:
                                error: "internal server error"

    /api/v1/conversation:
        post:
            tags: [Conversation]
            summary: Start or continue a conversation
            description: |
                Unified endpoint. Start by omitting conversation_id and providing symptom + language. Continue by sending conversation_id + answer.
            requestBody:
                required: true
                content:
                    application/json:
                        schema:
                            $ref: "#/components/schemas/ConversationRequest"
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: "#/components/schemas/ConversationResponse"
                "400":
                    description: Bad Request
                    content:
                        application/json:
                            schema:
                                $ref: "#/components/schemas/ErrorResponse"
                "404": { $ref: "#/components/responses/NotFound" }

    /api/v1/conversation/offline-topics:
        get:
            tags: [Conversation]
            summary: Get offline health topics
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                type: array
                                items:
                                    $ref: "#/components/schemas/HealthTopic"

    /api/v1/remedy:
        post:
            tags: [Remedy]
            summary: Get remedy from symptom input
            requestBody:
                required: true
                content:
                    application/json:
                        schema:
                            $ref: "#/components/schemas/RemedyRequest"
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: "#/components/schemas/RemedyResponse"
                "400":
                    description: Bad Request
                    content:
                        application/json:
                            schema:
                                $ref: "#/components/schemas/ErrorResponse"

    /api/v1/admin/topics:
        get:
            tags: [Topics]
            summary: List topics (paginated)
            security:
                - bearerAuth: []
            parameters:
                - in: query
                  name: page
                  schema: { type: integer, default: 1 }
                - in: query
                  name: limit
                  schema: { type: integer, default: 20 }
                - in: query
                  name: search
                  schema: { type: string }
                - in: query
                  name: sort_by
                  schema: { type: string, default: name_en }
                - in: query
                  name: sort_order
                  schema: { type: string, default: asc }
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: "#/components/schemas/PaginatedTopicsResult"
                "401": { $ref: "#/components/responses/Unauthorized" }

    /api/v1/admin/topic:
        post:
            tags: [Topics]
            summary: Create topic
            security:
                - bearerAuth: []
            requestBody:
                required: true
                content:
                    application/json:
                        schema:
                            $ref: "#/components/schemas/TopicCreateRequest"
            responses:
                "201":
                    description: Created
                    content:
                        application/json:
                            schema:
                                $ref: "#/components/schemas/Topic"
                "401": { $ref: "#/components/responses/Unauthorized" }

    /api/v1/admin/topics/{topic_key}:
        delete:
            tags: [Topics]
            summary: Delete (soft) topic by key
            security:
                - bearerAuth: []
            parameters:
                - in: path
                  name: topic_key
                  required: true
                  schema: { type: string }
            responses:
                "204":
                    description: No Content
                "401": { $ref: "#/components/responses/Unauthorized" }
                "404": { $ref: "#/components/responses/NotFound" }
        put:
            tags: [Topics]
            summary: Update topic by key
            security:
                - bearerAuth: []
            parameters:
                - in: path
                  name: topic_key
                  required: true
                  schema:
                      type: string
            requestBody:
                required: true
                content:
                    application/json:
                        schema:
                            $ref: "#/components/schemas/TopicUpdateRequest"
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: "#/components/schemas/Topic"
                "401": { $ref: "#/components/responses/Unauthorized" }
                "404": { $ref: "#/components/responses/NotFound" }

    /api/v1/admin/topic/{topic_key}:
        get:
            tags: [Topics]
            summary: Get topic by key
            security:
                - bearerAuth: []
            parameters:
                - in: path
                  name: topic_key
                  required: true
                  schema:
                      type: string
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: "#/components/schemas/Topic"
                "401": { $ref: "#/components/responses/Unauthorized" }
                "404": { $ref: "#/components/responses/NotFound" }

    /api/v1/feedbacks:
        post:
            tags: [Feedback]
            summary: Create public feedback
            requestBody:
                required: true
                content:
                    application/json:
                        schema:
                            $ref: "#/components/schemas/CreateFeedbackDTO"
            responses:
                "201":
                    description: Created
                    content:
                        application/json:
                            schema:
                                $ref: "#/components/schemas/Feedback"
                "400":
                    description: Bad Request
                    content:
                        application/json:
                            schema:
                                $ref: "#/components/schemas/ErrorResponse"

    /api/v1/admin/feedbacks:
        get:
            tags: [Admin/Feedback]
            summary: List feedbacks (admin)
            security:
                - bearerAuth: []
            parameters:
                - in: query
                  name: limit
                  schema: { type: integer, default: 20 }
                - in: query
                  name: offset
                  schema: { type: integer, default: 0 }
                - in: query
                  name: language
                  schema: { type: string }
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: "#/components/schemas/AdminFeedbackListResponse"
                "401": { $ref: "#/components/responses/Unauthorized" }

    /api/v1/admin/feedbacks/{id}:
        get:
            tags: [Admin/Feedback]
            summary: Get feedback by id (admin)
            security:
                - bearerAuth: []
            parameters:
                - in: path
                  name: id
                  required: true
                  schema: { type: string }
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: "#/components/schemas/Feedback"
                "401": { $ref: "#/components/responses/Unauthorized" }
                "404": { $ref: "#/components/responses/NotFound" }
        delete:
            tags: [Admin/Feedback]
            summary: Delete feedback by id (admin)
            security:
                - bearerAuth: []
            parameters:
                - in: path
                  name: id
                  required: true
                  schema: { type: string }
            responses:
                "204":
                    description: No Content
                "401": { $ref: "#/components/responses/Unauthorized" }
                "404": { $ref: "#/components/responses/NotFound" }

    /api/v1/admin/redflags:
        get:
            tags: [Admin/RedFlags]
            summary: List red flag rules
            security:
                - bearerAuth: []
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                type: object
                                properties:
                                    items:
                                        type: array
                                        items:
                                            $ref: "#/components/schemas/RedFlag"
                "401": { $ref: "#/components/responses/Unauthorized" }
        post:
            tags: [Admin/RedFlags]
            summary: Create red flag rule
            security:
                - bearerAuth: []
            requestBody:
                required: true
                content:
                    application/json:
                        schema:
                            $ref: "#/components/schemas/CreateRedFlagDTO"
            responses:
                "201":
                    description: Created
                    content:
                        application/json:
                            schema:
                                $ref: "#/components/schemas/RedFlag"
                "401": { $ref: "#/components/responses/Unauthorized" }

    /api/v1/admin/redflags/{id}:
        get:
            tags: [Admin/RedFlags]
            summary: Get red flag by id
            security:
                - bearerAuth: []
            parameters:
                - in: path
                  name: id
                  required: true
                  schema: { type: string }
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: "#/components/schemas/RedFlag"
                "401": { $ref: "#/components/responses/Unauthorized" }
                "404": { $ref: "#/components/responses/NotFound" }
        put:
            tags: [Admin/RedFlags]
            summary: Update red flag by id
            security:
                - bearerAuth: []
            parameters:
                - in: path
                  name: id
                  required: true
                  schema: { type: string }
            requestBody:
                required: true
                content:
                    application/json:
                        schema:
                            $ref: "#/components/schemas/UpdateRedFlagDTO"
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: "#/components/schemas/RedFlag"
                "401": { $ref: "#/components/responses/Unauthorized" }
                "404": { $ref: "#/components/responses/NotFound" }
        delete:
            tags: [Admin/RedFlags]
            summary: Delete red flag by id
            security:
                - bearerAuth: []
            parameters:
                - in: path
                  name: id
                  required: true
                  schema: { type: string }
            responses:
                "204":
                    description: No Content
                "401": { $ref: "#/components/responses/Unauthorized" }
                "404": { $ref: "#/components/responses/NotFound" }
